<template>
    <view class="view">
        <section class="nav-bar">
            <c-navBar title="游戏详情" isShadow isBack isPerch> </c-navBar>
        </section>
        <section class="content">
            <view class="swiper">
                <u-swiper :list="list" indicator indicatorMode="dot" height="320rpx" circular></u-swiper>
            </view>
            <view class="header">
                <view class="flex align-center justify-between">
                    <text class="mf-font-28 mf-weight-bold" style="color: #191919">活动名称活动名称活动名称</text>
                    <u-icon name="share-square" bold color="#191919" size="28rpx"></u-icon>
                </view>
                <view class="flex align-center justify-between" style="margin-top: 20rpx">
                    <text class="mf-font-28" style="color: #333333">开始时间：2025-7-9 12:45:30</text>
                    <view class="flex align-center gap-8" @click="$fn.jumpPage('/pages/tabbar/parse?title=游戏规则')">
                        <u-icon
                            name="error-circle"
                            color="#1D7BF7"
                            label="游戏规则"
                            label-color="#1d7bf7"
                            label-size="24rpx"
                            :top="1"
                            size="24rpx"
                        ></u-icon>
                    </view>
                </view>
            </view>
            <view class="prize">
                <text class="mf-font-28 mf-weight-bold" style="color: #191919">活动礼品</text>
                <view class="prize-item flex align-center gap-20">
                    <u-image
                        src="https://picsum.photos/96/96?random=1"
                        radius="6rpx"
                        width="96rpx"
                        height="96rpx"
                        mode="aspectFill"
                    ></u-image>
                    <view class="flex flex-col gap-20">
                        <text class="prize-title mf-font-28" style="color: #070f1a">苹果16pro max 256GB*1</text>
                        <text class="prize-desc mf-font-20" style="color: #606266">礼品数量：3台</text>
                    </view>
                </view>
            </view>
            <!-- 游戏信息 -->
            <view class="game-info flex align-center">
                <view class="item flex flex-col flex-center">
                    <text class="label">报名人数</text>
                    <text class="value">200</text>
                </view>
                <view class="item flex flex-col flex-center active">
                    <text class="label">当前轮次</text>
                    <text class="value">第二轮</text>
                </view>
                <view class="item flex flex-col flex-center">
                    <text class="label">已淘汰</text>
                    <text class="value">30</text>
                </view>
                <view class="item flex flex-col flex-center">
                    <text class="label">剩余</text>
                    <text class="value">170</text>
                </view>
            </view>
            <!-- 游戏状态 -->
            <view class="game-status">
                <!-- 待开始 -->
                <view class="flex flex-col flex-center gap-8">
                    <text class="mf-font-32 mf-weight-bold" style="color: #191919">等待游戏开始</text>
                    <text class="mf-font-24" style="color: #191919">{{ `报名人数：${1} 人` }}</text>
                </view>
                <!-- 下一轮倒计时 -->
                <view class="flex flex-col flex-center gap-8">
                    <text class="mf-font-32 mf-weight-bold" style="color: #191919">您已晋级第三轮</text>
                    <text class="mf-font-24" style="color: #191919">{{  }}</text>
                </view>
                <!-- 开始倒计时 -->
                <view class="flex flex-col flex-center gap-8">
                    <text class="mf-font-32 mf-weight-bold" style="color: #191919">第三轮倒计时</text>
                    <text class="mf-font-24" style="color: #191919">{{  }}</text>
                </view>
                <!-- 淘汰 -->
                <view class="flex flex-col flex-center gap-8">
                    <text class="mf-font-32 mf-weight-bold" style="color: #191919">您已被淘汰</text>
                    <text class="mf-font-24" style="color: #191919">别灰心！下次再参与 </text>
                </view>
                <!-- 获奖 -->
                <view class="flex flex-col flex-center gap-8">
                    <text class="mf-font-32 mf-weight-bold" style="color: #191919">您已获奖</text>
                    <text class="mf-font-24" style="color: #191919">恭喜您！快去领奖吧！</text>
                </view>
            </view>
            <!-- 游戏选择 -->
            <view class="game-choice flex align-center gap-12">
                <view class="left flex flex-center" :style="{ width: '50%' }">
                    <text class="mf-font-28" style="color: #fff">红色</text>
                </view>
                <view class="right flex flex-center" :style="{ width: '50%' }">
                    <text class="mf-font-28" style="color: #fff">蓝色</text>
                </view>
            </view>
            <!-- 倒计时显示 -->
            <view class="countdown-container flex align-center justify-center gap-12" v-if="gameDetail.status === 1">
                <text class="mf-font-24" style="color: #070f1a">开始倒计时</text>
                <view class="countdown-time flex align-center gap-8">
                    <view class="time-item">
                        <text class="time-num">{{ formatTime(gameDetail.countdown.hours) }}</text>
                    </view>
                    <text class="time-separator">:</text>
                    <view class="time-item">
                        <text class="time-num">{{ formatTime(gameDetail.countdown.minutes) }}</text>
                    </view>
                    <text class="time-separator">:</text>
                    <view class="time-item">
                        <text class="time-num">{{ formatTime(gameDetail.countdown.seconds) }}</text>
                    </view>
                </view>
            </view>
            <view class="btn flex align-center justify-between">
                <u-button
                    type="primary"
                    size="large"
                    :custom-style="{
                        backgroundColor: '#1D7BF7',
                        borderColor: '#1D7BF7',
                        borderRadius: '32rpx',
                        width: '414rpx',
                        height: '64rpx',
                    }"
                >
                    <view class="flex align-center gap-12">
                        <text class="mf-font-24" style="color: #fff">立即报名</text>
                    </view>
                </u-button>
                <u-button
                    type="plain"
                    size="large"
                    :custom-style="{
                        backgroundColor: '#1D7BF7',
                        borderColor: '#1D7BF7',
                        borderRadius: '32rpx',
                        width: '414rpx',
                        height: '64rpx',
                    }"
                >
                    <view class="flex align-center gap-12">
                        <text class="mf-font-24" style="color: #fff">获奖用户</text>
                    </view>
                </u-button>
                <u-button
                    type="primary"
                    size="large"
                    :custom-style="{
                        backgroundColor: '#1D7BF7',
                        borderColor: '#1D7BF7',
                        borderRadius: '32rpx',
                        width: '414rpx',
                        height: '64rpx',
                    }"
                >
                    <view class="flex align-center gap-12">
                        <text class="mf-font-24" style="color: #fff">领取奖品</text>
                    </view>
                </u-button>
            </view>
            <view class="bullet-comment">
                <c-bulletComment ref="bulletComment" class="bullet-content" @reloadDanmu="reloadDanmu"></c-bulletComment>
            </view>
            <view class="send-message">
                <u-input
                    v-model="bulletComment"
                    placeholder="说点什么..."
                    :customStyle="{
                        borderRadius: '36rpx',
                        height: '72rpx',
                        background: '#F5F6F7',
                        padding: '0 24rpx',
                    }"
                    border="none"
                    clearable
                    confirmType="send"
                    @confirm="sendBulletComment"
                />
            </view>
        </section>
    </view>
</template>

<script>
export default {
    data() {
        return {
            list: [
                "https://picsum.photos/750/750?random=1",
                "https://picsum.photos/750/750?random=2",
                "https://picsum.photos/750/750?random=3",
                "https://picsum.photos/750/750?random=4",
            ],
            // 游戏详情数据
            gameDetail: {
                id: 1,
                title: "庆祝门店十周年庆活动，满减券各种福利做游戏免费领",
                startTime: "2025-07-29 18:30:00", // 游戏开始时间
                status: 1, // 游戏状态 0: 进行中 1: 待开始 2: 已结束
                prize: "苹果16pro max 256GB*1",
                prizeCount: 3,
                countdown: {
                    hours: 0,
                    minutes: 0,
                    seconds: 0,
                },
                userStatus: null, // 0-已选，1-晋级，2-淘汰，3-获奖
                pcik: null, // 0-红方，1-蓝方
                redNum: 30,
                blueNum: 100,
            },
            totalSeconds: 0, // 淘汰-下一轮倒计时-秒
            countdownTimer: null, // 倒计时定时器
            bulletComment: "", // 弹幕相关
            danmuList: [], // 弹幕数据列表
            danmuContion: {
                // 弹幕查询条件
                page: 1,
                size: 200,
            },
            userInfo: {
                nickname: "游客",
                avatar: "https://picsum.photos/60/60?random=1",
            },
            // 弹幕重载控制
            lastReloadTime: 0, // 上次重载时间
            reloadCooldown: 5000, // 重载冷却时间 5秒
        };
    },
    onLoad(options) {
        // 获取游戏ID
        if (options.id) {
            this.gameDetail.id = options.id;
        }
        // 初始化倒计时
        this.initCountdown();
        // 初始化弹幕
        this.initBulletComment();
    },
    onShow() {
        // 页面显示时重新初始化倒计时
        this.initCountdown();
    },
    onHide() {
        // 页面隐藏时清除定时器
        this.clearCountdown();
    },
    onUnload() {
        // 页面卸载时清除定时器
        this.clearCountdown();
    },
    methods: {
        // 初始化倒计时
        initCountdown() {
            // 只有状态为1（待开始）的游戏才需要倒计时
            if (this.gameDetail.status !== 1) {
                return;
            }

            // 解析开始时间
            const startTime = new Date(this.gameDetail.startTime).getTime();
            const currentTime = new Date().getTime();

            // 只有开始时间大于当前时间才开始倒计时
            if (startTime <= currentTime) {
                // 如果开始时间已过，更新状态为进行中
                this.gameDetail.status = 0;
                return;
            }

            // 计算剩余秒数
            const remainingSeconds = Math.floor((startTime - currentTime) / 1000);
            this.totalSeconds = remainingSeconds;

            // 更新倒计时显示
            this.updateCountdownDisplay();

            // 开始倒计时
            this.startCountdown();
        },
        // 开始倒计时
        startCountdown() {
            // 清除之前的定时器
            this.clearCountdown();

            // 开始倒计时
            this.countdownTimer = setInterval(() => {
                if (this.totalSeconds > 0) {
                    this.totalSeconds--;
                    this.updateCountdownDisplay();
                } else {
                    // 倒计时结束，游戏开始
                    this.clearCountdown();
                    this.onCountdownEnd();
                }
            }, 1000);
        },
        // 更新倒计时显示
        updateCountdownDisplay() {
            const hours = Math.floor(this.totalSeconds / 3600);
            const minutes = Math.floor((this.totalSeconds % 3600) / 60);
            const seconds = this.totalSeconds % 60;

            this.gameDetail.countdown = {
                hours,
                minutes,
                seconds,
            };
        },
        // 清除倒计时
        clearCountdown() {
            if (this.countdownTimer) {
                clearInterval(this.countdownTimer);
                this.countdownTimer = null;
            }
        },
        // 倒计时结束处理
        onCountdownEnd() {
            // 更新游戏状态为进行中
            this.gameDetail.status = 0;

            uni.showToast({
                title: "游戏开始！",
                icon: "success",
            });
        },
        // 格式化时间显示（补零）
        formatTime(time) {
            return time.toString().padStart(2, "0");
        },

        // ========== 弹幕相关方法 ==========

        // 获取弹幕列表
        async getBarrageList(isInit) {
            try {
                // TODO: 替换为实际的API调用
                // let res = await getBarrageListApi(this.danmuContion)
                // let resData = (res && res.data) || {}
                // let list = Array.isArray(resData.records) ? resData.records : []

                // 模拟API数据
                let list = [];
                for (let i = 0; i < 20; i++) {
                    list.push({
                        nickname: `用户${i + 1}`,
                        voteName: this.gameDetail.title || "游戏",
                        avatarUrl: `https://picsum.photos/44/44?random=${i}`,
                        content: `用户${i + 1} 已为《${this.gameDetail.title || "游戏"}》投下宝贵的一票`,
                    });
                }

                // 处理弹幕数据
                list.map((item) => {
                    item.color = "#000000"; // 黑色文字
                    item.timestampt = new Date().getTime();
                    // 不添加图片，只保留文字内容
                    item.content = `${item.nickname} 已为《${item.voteName}》投下宝贵的一票`;
                });

                let danmuLength = this.danmuList.length;
                this.danmuList = list;
                this.addBarrage(isInit || danmuLength === 0);
            } catch (e) {
                console.error("查询弹幕列表失败:", e);
                uni.showToast({
                    title: (e && e.message) || "查询弹幕列表失败",
                    icon: "none",
                    duration: 2000,
                });
            }
        },

        // 添加弹幕到组件
        addBarrage(isInit) {
            if (!isInit || !this.danmuList.length) {
                return;
            }

            const barrageComp = this.$refs && this.$refs.bulletComment;
            if (barrageComp) {
                barrageComp.getBarrageInstance({
                    duration: 15, // 弹幕动画时长
                    lineHeight: 1.8, // 弹幕行高，更紧凑
                    padding: [5, 5, 5, 5], // 弹幕区四周留白
                    alpha: 1, // 全局透明度
                    font: "10px PingFang SC", // 全局字体
                    range: [0, 1], // 弹幕显示的垂直范围
                    tunnelShow: false, // 不显示轨道线
                    tunnelMaxNum: 100, // 隧道最大缓冲长度
                    maxLength: 50, // 弹幕最大字节长度
                    safeGap: 10, // 发送时的安全间隔
                    enableTap: false, // 不允许点击弹幕
                    danmuList: this.danmuList,
                });
            }
        },

        // 初始化弹幕组件
        initBulletComment() {
            this.$nextTick(() => {
                // 获取弹幕数据并初始化
                this.getBarrageList(true);
            });
        },

        // 加载初始弹幕数据（模拟数据）
        loadInitialDanmu() {
            const initialDanmu = [];

            // 生成15条初始弹幕
            for (let i = 0; i < 15; i++) {
                initialDanmu.push({
                    content: "这是弹幕",
                    color: "#000000", // 黑色文字，不要图片
                });
            }

            // 添加到弹幕列表
            this.danmuList = [...initialDanmu];

            // 分批随机加载弹幕，增加随机性
            this.loadDanmuRandomly(initialDanmu);
        },

        // 发送弹幕
        sendBulletComment() {
            if (!this.bulletComment.trim()) {
                uni.showToast({
                    title: "请输入弹幕内容",
                    icon: "none",
                });
                return;
            }

            // 构造弹幕数据
            const newDanmu = {
                content: this.bulletComment.trim(),
                color: "#000000", // 黑色文字，不要图片
            };

            // 本地发送弹幕
            this.sendDanmuLocal(newDanmu);

            // 预留接口调用位置
            // this.sendDanmuToServer(newDanmu);

            // 清空输入框
            this.bulletComment = "";
        },

        // 本地发送弹幕
        sendDanmuLocal(danmuData) {
            try {
                // 添加到本地弹幕列表
                this.danmuList.push(danmuData);

                // 发送到弹幕组件
                if (this.$refs.bulletComment) {
                    this.$refs.bulletComment.addData([danmuData]);
                }
            } catch (error) {
                console.error("弹幕发送失败:", error);
                uni.showToast({
                    title: "弹幕发送失败",
                    icon: "none",
                });
            }
        },

        // 预留：发送弹幕到服务器
        async sendDanmuToServer(danmuData) {
            try {
            } catch (error) {
                console.error("发送弹幕到服务器失败:", error);
                // 可以在这里处理错误，比如显示错误提示
            }
        },

        // 弹幕重新加载回调
        async reloadDanmu(type) {
            const currentTime = Date.now();

            // 检查冷却时间，避免频繁重载
            if (currentTime - this.lastReloadTime < this.reloadCooldown) {
                return;
            }
            this.lastReloadTime = currentTime;

            const barrageComp = this.$refs && this.$refs.bulletComment;
            if (type === "addDanmu") {
                // 继续添加更多弹幕
                await this.getBarrageList(false);
                if (barrageComp) {
                    barrageComp.open();
                    barrageComp.addData(this.danmuList);
                }
            } else {
                // 重新初始化弹幕，加载完整的弹幕数据
                await this.getBarrageList(true);
            }
        },

        // 预留：从服务器获取弹幕数据
        async getDanmuFromServer() {
            try {
                // TODO: 调用后端接口获取弹幕
                const response = await uni.request({
                    url: "/api/game/danmu/list",
                    method: "GET",
                    data: {
                        gameId: this.gameDetail.id,
                        page: 1,
                        limit: 10,
                    },
                });

                if (response.data.code === 200) {
                    const danmuList = response.data.data || [];
                    if (this.$refs.bulletComment && danmuList.length > 0) {
                        this.$refs.bulletComment.addData(danmuList);
                    }
                }
            } catch (error) {
                console.error("获取弹幕数据失败:", error);
            }
        },

        // 获取随机颜色
        getRandomColor() {
            const colors = [
                "#FF6B6B",
                "#4ECDC4",
                "#45B7D1",
                "#96CEB4",
                "#FFEAA7",
                "#DDA0DD",
                "#98D8C8",
                "#F7DC6F",
                "#BB8FCE",
                "#85C1E9",
                "#F8C471",
                "#82E0AA",
            ];
            return colors[Math.floor(Math.random() * colors.length)];
        },
    },
};
</script>

<style lang="scss" scoped>
.view {
    .content {
        padding: 24rpx 32rpx;
        padding-bottom: calc(env(safe-area-inset-bottom) + 20rpx);
        .header {
            margin-top: 24rpx;
        }
        .prize {
            margin-top: 32rpx;
            .prize-item {
                margin-top: 24rpx;
                padding: 12rpx;
                border-radius: 12rpx;
                background: #f7f8fa;
            }
        }
        .game-info {
            border-radius: 8rpx;
            background: #1d7bf750;
            color: #fff;
            margin-top: 24rpx;
            .item {
                flex: 1;
                padding: 12rpx;

                &.active {
                    padding: 12rpx;
                    border-radius: 8rpx;
                    background: #1d7bf7;
                }
                .lable {
                    font-size: 24rpx;
                }
                .value {
                    font-size: 24rpx;
                }
            }
        }
        .game-status {
            margin-top: 40rpx;
        }
        .game-choice {
            margin-top: 24rpx;
            .left {
                height: 80rpx;
                border-radius: 16rpx 0 0 16rpx;
                background: linear-gradient(90deg, #f71914 0%, #f5582b 100%);
            }
            .right {
                height: 80rpx;
                border-radius: 0 16rpx 16rpx 0;
                background: linear-gradient(90deg, #2171fe 0%, #0435ff 100%);
            }
        }
        // 倒计时样式
        .countdown-container {
            margin-top: 22rpx;
            .countdown-time {
                .time-item {
                    background: #f50c0c;
                    border-radius: 8rpx;
                    padding: 8rpx;
                    display: flex;
                    align-items: center;
                    justify-content: center;

                    .time-num {
                        font-size: 24rpx;
                        font-weight: bold;
                        color: #fff;
                        line-height: 1;
                    }
                }

                .time-separator {
                    font-size: 24rpx;
                    font-weight: bold;
                    color: #f50c0c;
                    line-height: 1;
                }
            }
        }

        .btn {
            margin-top: 56rpx;
        }
        .bullet-comment {
            width: 100%;
            height: 350rpx;
            margin-top: 40rpx;
            .bullet-content {
                width: 100%;
                height: 350rpx;
                box-sizing: border-box;
            }
        }
        .send-message {
            margin-top: 40rpx;
        }
    }
}
</style>
