<template>
    <view class="view">
        <section class="nav-bar">
            <c-navBar title="游戏详情" isShadow isBack isPerch> </c-navBar>
        </section>
        <section class="content">
            <view class="swiper">
                <u-swiper :list="list" indicator indicatorMode="dot" height="320rpx" circular></u-swiper>
            </view>
            <view class="header">
                <view class="flex align-center justify-between">
                    <text class="mf-font-28 mf-weight-bold" style="color: #191919">{{ gameDetail.title }}</text>
                    <u-icon name="share-square" bold color="#191919" size="28rpx" @click="shareGame"></u-icon>
                </view>
                <view class="flex align-center justify-between" style="margin-top: 20rpx">
                    <text class="mf-font-28" style="color: #333333">开始时间：{{ gameDetail.startTime }}</text>
                    <view class="flex align-center gap-8" @click="$fn.jumpPage('/pages/tabbar/parse?title=游戏规则')">
                        <u-icon
                            name="error-circle"
                            color="#1D7BF7"
                            label="游戏规则"
                            label-color="#1d7bf7"
                            label-size="24rpx"
                            :top="1"
                            size="24rpx"
                        ></u-icon>
                    </view>
                </view>
            </view>
            <view class="prize">
                <text class="mf-font-28 mf-weight-bold" style="color: #191919">活动礼品</text>
                <view class="prize-item flex align-center gap-20">
                    <u-image
                        src="https://picsum.photos/96/96?random=1"
                        radius="6rpx"
                        width="96rpx"
                        height="96rpx"
                        mode="aspectFill"
                    ></u-image>
                    <view class="flex flex-col gap-20">
                        <text class="prize-title mf-font-28" style="color: #070f1a">{{ gameDetail.prize }}</text>
                        <text class="prize-desc mf-font-20" style="color: #606266">礼品数量：{{ gameDetail.prizeCount }}台</text>
                    </view>
                </view>
            </view>
            <!-- 游戏信息 -->
            <view class="game-info flex align-center">
                <view class="item flex flex-col flex-center">
                    <text class="label">报名人数</text>
                    <text class="value">{{ gameDetail.signUpCount }}</text>
                </view>
                <view class="item flex flex-col flex-center active">
                    <text class="label">当前轮次</text>
                    <text class="value">第{{ gameDetail.currentRound }}轮</text>
                </view>
                <view class="item flex flex-col flex-center">
                    <text class="label">已淘汰</text>
                    <text class="value">{{ gameDetail.eliminatedCount }}</text>
                </view>
                <view class="item flex flex-col flex-center">
                    <text class="label">剩余</text>
                    <text class="value">{{ gameDetail.remainingCount }}</text>
                </view>
            </view>
            <!-- 游戏状态 -->
            <view class="game-status">
                <!-- 报名阶段 -->
                <view class="flex flex-col flex-center gap-8" v-if="gameDetail.phase === 'signup'">
                    <text class="mf-font-32 mf-weight-bold" style="color: #191919">报名倒计时</text>
                    <text class="mf-font-24" style="color: #191919">{{ formatCountdown(gameDetail.signUpCountdown) }}</text>
                    <text class="mf-font-20" style="color: #666">报名结束后游戏正式开始</text>
                </view>

                <!-- 轮次进行中 - 用户可选择 -->
                <view class="flex flex-col flex-center gap-8" v-if="gameDetail.phase === 'round' && gameDetail.userStatus === 'playing' && gameDetail.pick === null">
                    <text class="mf-font-32 mf-weight-bold" style="color: #191919">第{{ gameDetail.currentRound }}轮</text>
                    <text class="mf-font-24" style="color: #191919">{{ formatCountdown(gameDetail.roundCountdown) }}</text>
                    <text class="mf-font-20" style="color: #666">请选择红方或蓝方</text>
                </view>

                <!-- 轮次进行中 - 用户已选择 -->
                <view class="flex flex-col flex-center gap-8" v-if="gameDetail.phase === 'round' && gameDetail.pick !== null && gameDetail.userStatus === 'playing'">
                    <text class="mf-font-32 mf-weight-bold" style="color: #191919">第{{ gameDetail.currentRound }}轮</text>
                    <text class="mf-font-24" style="color: #191919">{{ formatCountdown(gameDetail.roundCountdown) }}</text>
                    <text class="mf-font-20" style="color: #666">您已选择{{ gameDetail.pick === 0 ? '红方' : '蓝方' }}，等待结果...</text>
                </view>

                <!-- 用户被淘汰 -->
                <view class="flex flex-col flex-center gap-8" v-if="gameDetail.userStatus === 'eliminated'">
                    <text class="mf-font-32 mf-weight-bold" style="color: #f56c6c">您已被淘汰</text>
                    <text class="mf-font-24" style="color: #191919">第{{ gameDetail.currentRound }}轮未选择或选择失败</text>
                    <text class="mf-font-20" style="color: #666">感谢参与，下次再来！</text>
                </view>

                <!-- 用户获奖 -->
                <view class="flex flex-col flex-center gap-8" v-if="gameDetail.userStatus === 'winner'">
                    <text class="mf-font-32 mf-weight-bold" style="color: #67c23a">🎉 恭喜获奖！</text>
                    <text class="mf-font-24" style="color: #191919">您是最终的{{ gameDetail.winners.length }}名获奖者之一</text>
                    <text class="mf-font-20" style="color: #666">快去领取您的奖品吧！</text>
                </view>

                <!-- 游戏结束 -->
                <view class="flex flex-col flex-center gap-8" v-if="gameDetail.phase === 'finished' && gameDetail.userStatus !== 'winner'">
                    <text class="mf-font-32 mf-weight-bold" style="color: #191919">游戏已结束</text>
                    <text class="mf-font-24" style="color: #191919">共{{ gameDetail.winners.length }}名用户获奖</text>
                    <text class="mf-font-20" style="color: #666">感谢参与！</text>
                </view>
            </view>
            <!-- 报名按钮 -->
            <view class="signup-container" v-if="gameDetail.phase === 'signup'">
                <view
                    class="signup-btn flex flex-center"
                    @click="signUp"
                    v-if="!gameDetail.isSignedUp"
                >
                    <text class="mf-font-32 mf-weight-bold" style="color: #fff">立即报名</text>
                </view>
                <view class="signed-up flex flex-center" v-else>
                    <text class="mf-font-32 mf-weight-bold" style="color: #67c23a">✓ 已报名</text>
                </view>
            </view>

            <!-- 游戏选择区域 -->
            <view class="game-choice-container" v-if="gameDetail.phase === 'round' && gameDetail.userStatus === 'playing'">
                <!-- 选择按钮 - 只在用户未选择时显示 -->
                <view class="game-choice flex align-center gap-24" v-if="gameDetail.pick === null">
                    <view class="choice-btn left flex flex-center" @click="makeChoice(0)">
                        <text class="choice-text mf-font-28" style="color: #fff">红方</text>
                    </view>
                    <view class="choice-btn right flex flex-center" @click="makeChoice(1)">
                        <text class="choice-text mf-font-28" style="color: #fff">蓝方</text>
                    </view>
                </view>

                <!-- 已选择状态显示 - 倒计时期间不显示人数 -->
                <view class="choice-result flex align-center" v-if="gameDetail.pick !== null">
                    <view
                        class="result-btn left flex flex-center"
                        :style="{ width: '50%' }"
                        :class="{ 'user-selected': gameDetail.pick === 0 }"
                    >
                        <text class="choice-text mf-font-28" style="color: #fff">红方</text>
                    </view>
                    <view
                        class="result-btn right flex flex-center"
                        :style="{ width: '50%' }"
                        :class="{ 'user-selected': gameDetail.pick === 1 }"
                    >
                        <text class="choice-text mf-font-28" style="color: #fff">蓝方</text>
                    </view>
                </view>
            </view>

            <!-- 轮次结果展示 -->
            <view class="round-result-container" v-if="gameDetail.showRoundResult && gameDetail.userStatus === 'playing'">
                <view class="result-title flex align-center justify-center" style="margin-bottom: 24rpx;">
                    <text class="mf-font-32 mf-weight-bold" style="color: #070f1a">您已晋级第{{ gameDetail.currentRound }}轮</text>
                </view>
                <view class="result-subtitle flex align-center justify-center" style="margin-bottom: 32rpx;">
                    <text class="mf-font-24" style="color: #666">下一轮倒计时: {{ gameConfig.roundDuration }}s</text>
                </view>

                <!-- 双方人数展示 -->
                <view class="choice-result flex align-center" style="margin-bottom: 32rpx;">
                    <view
                        class="result-btn left flex flex-col flex-center"
                        :style="{ width: getChoiceWidth(0) }"
                        :class="{ 'user-selected': gameDetail.pick === 0 }"
                    >
                        <text class="choice-text mf-font-28" style="color: #fff">{{ gameDetail.redNum > gameDetail.blueNum ? '晋级' : '淘汰' }}</text>
                        <text class="choice-count mf-font-20" style="color: #fff">{{ gameDetail.redNum }}人({{ Math.round(gameDetail.redNum / (gameDetail.redNum + gameDetail.blueNum) * 100) }}%)</text>
                    </view>
                    <view
                        class="result-btn right flex flex-col flex-center"
                        :style="{ width: getChoiceWidth(1) }"
                        :class="{ 'user-selected': gameDetail.pick === 1 }"
                    >
                        <text class="choice-text mf-font-28" style="color: #fff">{{ gameDetail.blueNum > gameDetail.redNum ? '晋级' : '淘汰' }}</text>
                        <text class="choice-count mf-font-20" style="color: #fff">{{ gameDetail.blueNum }}人({{ Math.round(gameDetail.blueNum / (gameDetail.redNum + gameDetail.blueNum) * 100) }}%)</text>
                    </view>
                </view>

                <!-- 继续下一轮按钮 -->
                <view class="continue-btn-container" v-if="gameDetail.canContinue">
                    <u-button
                        type="primary"
                        size="large"
                        :custom-style="{
                            backgroundColor: '#1d7bf7',
                            borderColor: '#1d7bf7',
                            borderRadius: '32rpx',
                            width: '100%',
                            height: '64rpx',
                        }"
                        @click="continueNextRound"
                    >
                        <view class="flex align-center gap-12">
                            <text class="mf-font-24" style="color: #fff">继续下一轮</text>
                        </view>
                    </u-button>
                </view>
            </view>

            <!-- 倒计时显示 -->
            <view
                class="countdown-container flex align-center justify-center gap-12"
                v-if="gameDetail.phase === 'round' && gameDetail.userStatus === 'playing' && !gameDetail.showRoundResult"
            >
                <text class="mf-font-24" style="color: #070f1a">本轮倒计时</text>
                <view class="countdown-time flex align-center gap-8">
                    <view class="time-item">
                        <text class="time-num">{{ formatTime(Math.floor(gameDetail.roundCountdown / 60)) }}</text>
                    </view>
                    <text class="time-separator">:</text>
                    <view class="time-item">
                        <text class="time-num">{{ formatTime(gameDetail.roundCountdown % 60) }}</text>
                    </view>
                </view>
            </view>
            <!-- 操作按钮 -->
            <view class="btn-container">
                <!-- 报名按钮 - 游戏待开始且用户未报名 -->
                <view class="btn flex align-center justify-center" v-if="gameDetail.status === 1 && !gameDetail.isSignedUp">
                    <u-button
                        type="primary"
                        size="large"
                        :custom-style="{
                            backgroundColor: '#1D7BF7',
                            borderColor: '#1D7BF7',
                            borderRadius: '32rpx',
                            width: '100%',
                            height: '64rpx',
                        }"
                        @click="signUp"
                    >
                        <view class="flex align-center gap-12">
                            <text class="mf-font-24" style="color: #fff">立即报名</text>
                        </view>
                    </u-button>
                </view>

                <!-- 已报名状态 -->
                <view class="btn flex align-center justify-center" v-if="gameDetail.status === 1 && gameDetail.isSignedUp">
                    <u-button
                        type="primary"
                        size="large"
                        :custom-style="{
                            backgroundColor: '#999',
                            borderColor: '#999',
                            borderRadius: '32rpx',
                            width: '100%',
                            height: '64rpx',
                        }"
                        disabled
                    >
                        <view class="flex align-center gap-12">
                            <text class="mf-font-24" style="color: #fff">已报名</text>
                        </view>
                    </u-button>
                </view>

                <!-- 双按钮布局 - 查看获奖用户和领取奖品 -->
                <view class="btn flex align-center justify-between gap-24" v-if="gameDetail.userStatus === 3">
                    <u-button
                        type="plain"
                        size="large"
                        :custom-style="{
                            backgroundColor: 'transparent',
                            borderColor: '#1D7BF7',
                            borderRadius: '32rpx',
                            width: '48%',
                            height: '64rpx',
                        }"
                        @click="viewWinners"
                    >
                        <view class="flex align-center gap-12">
                            <text class="mf-font-24" style="color: #1d7bf7">获奖用户</text>
                        </view>
                    </u-button>
                    <u-button
                        type="primary"
                        size="large"
                        :custom-style="{
                            backgroundColor: '#1D7BF7',
                            borderColor: '#1D7BF7',
                            borderRadius: '32rpx',
                            width: '48%',
                            height: '64rpx',
                        }"
                        @click="claimPrize"
                    >
                        <view class="flex align-center gap-12">
                            <text class="mf-font-24" style="color: #fff">领取奖品</text>
                        </view>
                    </u-button>
                </view>

                <!-- 查看获奖用户按钮 - 其他状态 -->
                <view class="btn flex align-center justify-center" v-if="gameDetail.userStatus !== 3 && gameDetail.status !== 1">
                    <u-button
                        type="plain"
                        size="large"
                        :custom-style="{
                            backgroundColor: 'transparent',
                            borderColor: '#1D7BF7',
                            borderRadius: '32rpx',
                            width: '100%',
                            height: '64rpx',
                        }"
                        @click="viewWinners"
                    >
                        <view class="flex align-center gap-12">
                            <text class="mf-font-24" style="color: #1d7bf7">查看获奖用户</text>
                        </view>
                    </u-button>
                </view>
            </view>
            <view class="bullet-comment">
                <c-bulletComment ref="bulletComment" class="bullet-content" @reloadDanmu="reloadDanmu"></c-bulletComment>
            </view>
            <view class="send-message">
                <u-input
                    v-model="bulletComment"
                    placeholder="说点什么..."
                    :customStyle="{
                        borderRadius: '36rpx',
                        height: '72rpx',
                        background: '#F5F6F7',
                        padding: '0 24rpx',
                    }"
                    border="none"
                    clearable
                    confirmType="send"
                    @confirm="sendBulletComment"
                />
            </view>

            <!-- 测试按钮 - 开发阶段使用 -->
            <view class="test-buttons" style="margin-top: 40rpx; padding: 20rpx; background: #f0f0f0; border-radius: 12rpx">
                <text style="font-size: 24rpx; color: #666; margin-bottom: 20rpx">游戏测试功能：</text>
                <view class="flex align-center gap-12" style="flex-wrap: wrap; margin-bottom: 20rpx">
                    <u-button size="mini" type="primary" @click="testChangePhase('signup')">报名阶段</u-button>
                    <u-button size="mini" type="success" @click="testChangePhase('round')">轮次阶段</u-button>
                    <u-button size="mini" type="error" @click="testChangeStatus('eliminated')">淘汰状态</u-button>
                    <u-button size="mini" type="warning" @click="testChangeStatus('winner')">获奖状态</u-button>
                </view>
                <view class="flex align-center gap-12" style="flex-wrap: wrap">
                    <u-button size="mini" @click="testResetGame">重置游戏</u-button>
                    <u-button size="mini" @click="testNextRound">下一轮</u-button>
                    <u-button size="mini" @click="testSignUp">模拟报名</u-button>
                    <u-button size="mini" @click="testForceRoundEnd">强制结束轮次</u-button>
                    <u-button size="mini" @click="testShowRoundResult">显示结果</u-button>
                </view>
            </view>
        </section>
    </view>
</template>

<script>
export default {
    data() {
        return {
            list: [
                "https://picsum.photos/750/750?random=1",
                "https://picsum.photos/750/750?random=2",
                "https://picsum.photos/750/750?random=3",
                "https://picsum.photos/750/750?random=4",
            ],
            // ========== 游戏配置变量 ==========
            gameConfig: {
                roundDuration: 10, // 每轮倒计时秒数（可配置）
                winnerCount: 3,    // 获奖人数（可配置）
                signUpDuration: 300, // 报名倒计时秒数（5分钟）
            },

            // 游戏详情数据
            gameDetail: {
                id: 1,
                title: "庆祝门店十周年庆活动，满减券各种福利做游戏免费领",
                startTime: "2025-07-30 20:30:00", // 游戏开始时间

                // 游戏状态: 0-报名中, 1-游戏进行中, 2-已结束
                status: 0,

                // 游戏阶段: signup-报名阶段, round-轮次进行中, result-结果展示, finished-游戏结束
                phase: 'signup',

                prize: "苹果16pro max 256GB*1",
                prizeCount: 3,
                signUpCount: 0, // 报名人数
                isSignedUp: false, // 用户是否已报名
                currentRound: 1, // 当前轮次

                // 倒计时相关
                signUpCountdown: 300, // 报名倒计时（秒）
                roundCountdown: 0, // 轮次倒计时（秒）

                // 用户状态: null-未选择, playing-游戏中, eliminated-已淘汰, winner-获奖
                userStatus: null,
                pick: null, // 0-红方，1-蓝方

                // 当前轮次参与人数
                redNum: 0, // 红方人数
                blueNum: 0, // 蓝方人数

                // 统计数据
                totalSignUp: 0, // 总报名人数
                eliminatedCount: 0, // 已淘汰人数
                remainingCount: 0, // 剩余人数

                // 获奖用户列表
                winners: [],

                // 轮次结果展示相关
                showRoundResult: false, // 是否显示轮次结果
                canContinue: false, // 是否可以继续下一轮（用户是否晋级）
            },
            totalSeconds: 0, // 淘汰-下一轮倒计时-秒
            countdownTimer: null, // 倒计时定时器
            bulletComment: "", // 弹幕相关
            danmuList: [], // 弹幕数据列表
            danmuContion: {
                // 弹幕查询条件
                page: 1,
                size: 200,
            },
            userInfo: {
                nickname: "游客",
                avatar: "https://picsum.photos/60/60?random=1",
            },
            // 弹幕重载控制
            lastReloadTime: 0, // 上次重载时间
            reloadCooldown: 5000, // 重载冷却时间 5秒
        };
    },
    onLoad(options) {
        // 获取游戏ID
        if (options.id) {
            this.gameDetail.id = options.id;
        }

        // 初始化游戏数据（模拟从服务器获取）
        this.initGameData();

        // 初始化游戏倒计时
        this.initGameCountdown();

        // 初始化弹幕
        this.initBulletComment();
    },
    onShow() {
        // 页面显示时重新初始化倒计时
        if (this.gameDetail.phase === 'signup' || this.gameDetail.phase === 'round') {
            this.initGameCountdown();
        }
    },
    onHide() {
        // 页面隐藏时清除定时器
        this.clearCountdown();
    },
    onUnload() {
        // 页面卸载时清除定时器
        this.clearCountdown();
    },
    methods: {
        // ========== 初始化方法 ==========

        // 初始化游戏数据
        initGameData() {
            // TODO: 实际项目中应该从服务器获取游戏数据
            // 这里使用静态数据模拟

            // 根据当前时间判断游戏阶段
            const now = new Date().getTime();
            const startTime = new Date(this.gameDetail.startTime).getTime();

            if (now < startTime) {
                // 报名阶段
                this.gameDetail.phase = 'signup';
                this.gameDetail.signUpCountdown = this.gameConfig.signUpDuration;
                this.gameDetail.signUpCount = Math.floor(Math.random() * 50) + 10; // 模拟已有报名人数
            } else {
                // 游戏已开始，模拟进行中状态
                this.gameDetail.phase = 'round';
                this.gameDetail.userStatus = 'playing';
                this.gameDetail.signUpCount = 200; // 模拟总报名人数
                this.gameDetail.totalSignUp = 200;
                this.gameDetail.remainingCount = 150; // 模拟当前剩余人数
                this.gameDetail.isSignedUp = true; // 假设用户已报名
            }
        },

        // ========== 倒计时相关方法 ==========

        // 初始化游戏倒计时
        initGameCountdown() {
            this.clearCountdown();

            if (this.gameDetail.phase === 'signup') {
                // 报名阶段倒计时
                this.startSignUpCountdown();
            } else if (this.gameDetail.phase === 'round') {
                // 轮次倒计时
                this.startRoundCountdown();
            }
        },

        // 开始报名倒计时
        startSignUpCountdown() {
            this.countdownTimer = setInterval(() => {
                if (this.gameDetail.signUpCountdown > 0) {
                    this.gameDetail.signUpCountdown--;
                } else {
                    // 报名结束，开始游戏
                    this.clearCountdown();
                    this.onSignUpEnd();
                }
            }, 1000);
        },

        // 开始轮次倒计时
        startRoundCountdown() {
            this.gameDetail.roundCountdown = this.gameConfig.roundDuration;

            // 重置红蓝人数显示（倒计时期间不显示人数）
            this.gameDetail.redNum = 0;
            this.gameDetail.blueNum = 0;

            this.countdownTimer = setInterval(() => {
                if (this.gameDetail.roundCountdown > 0) {
                    this.gameDetail.roundCountdown--;
                } else {
                    // 轮次结束
                    this.clearCountdown();
                    this.onRoundEnd();
                }
            }, 1000);
        },

        // 清除倒计时
        clearCountdown() {
            if (this.countdownTimer) {
                clearInterval(this.countdownTimer);
                this.countdownTimer = null;
            }
        },

        // 报名结束处理
        onSignUpEnd() {
            if (!this.gameDetail.isSignedUp) {
                uni.showToast({
                    title: "未报名，无法参与游戏",
                    icon: "none",
                });
                this.gameDetail.phase = 'finished';
                return;
            }

            // 开始第一轮游戏
            this.gameDetail.phase = 'round';
            this.gameDetail.userStatus = 'playing';
            this.gameDetail.totalSignUp = this.gameDetail.signUpCount;
            this.gameDetail.remainingCount = this.gameDetail.signUpCount;

            uni.showToast({
                title: "游戏开始！第1轮",
                icon: "success",
            });

            // 开始轮次倒计时
            this.startRoundCountdown();
        },

        // 轮次结束处理
        onRoundEnd() {
            if (this.gameDetail.userStatus !== 'playing') {
                return; // 用户已被淘汰或获奖
            }

            // 检查用户是否选择
            if (this.gameDetail.pick === null) {
                // 未选择，直接淘汰
                this.gameDetail.userStatus = 'eliminated';
                this.gameDetail.phase = 'finished';
                uni.showToast({
                    title: "未选择，已被淘汰",
                    icon: "none",
                });
                return;
            }

            // 计算轮次结果并显示结果页面
            this.calculateRoundResult();
        },
        // ========== 游戏核心逻辑方法 ==========

        // 计算轮次结果
        calculateRoundResult() {
            // 模拟红蓝双方人数（实际应从服务器获取）
            const totalPlayers = Math.max(this.gameDetail.remainingCount, 100);
            const redCount = Math.floor(Math.random() * totalPlayers * 0.6) + Math.floor(totalPlayers * 0.2);
            const blueCount = totalPlayers - redCount;

            this.gameDetail.redNum = redCount;
            this.gameDetail.blueNum = blueCount;

            // 判断获胜方
            const userWins = (this.gameDetail.pick === 0 && redCount > blueCount) ||
                           (this.gameDetail.pick === 1 && blueCount > redCount);

            if (userWins) {
                // 用户选择的一方获胜
                const winnerCount = this.gameDetail.pick === 0 ? redCount : blueCount;

                // 检查是否达到获奖人数
                if (winnerCount <= this.gameConfig.winnerCount) {
                    // 获奖了！
                    this.gameDetail.userStatus = 'winner';
                    this.gameDetail.winners.push({
                        userId: 'current_user',
                        nickname: this.userInfo.nickname,
                        round: this.gameDetail.currentRound
                    });
                    this.gameDetail.phase = 'finished';
                    this.gameDetail.showRoundResult = false;

                    uni.showToast({
                        title: "🎉 恭喜获奖！",
                        icon: "success",
                        duration: 3000
                    });
                } else {
                    // 晋级，显示结果页面
                    this.gameDetail.showRoundResult = true;
                    this.gameDetail.canContinue = true;
                    this.gameDetail.remainingCount = winnerCount;
                    this.gameDetail.eliminatedCount = this.gameDetail.totalSignUp - winnerCount;

                    uni.showToast({
                        title: `恭喜晋级！`,
                        icon: "success",
                    });
                }
            } else {
                // 用户选择的一方失败，被淘汰
                this.gameDetail.userStatus = 'eliminated';
                this.gameDetail.phase = 'finished';
                const loserCount = this.gameDetail.pick === 0 ? redCount : blueCount;
                this.gameDetail.eliminatedCount += loserCount;

                uni.showToast({
                    title: "很遗憾被淘汰",
                    icon: "none",
                    duration: 2000
                });
            }
        },

        // 格式化倒计时显示
        formatCountdown(seconds) {
            const minutes = Math.floor(seconds / 60);
            const remainingSeconds = seconds % 60;
            return `${minutes.toString().padStart(2, "0")}:${remainingSeconds.toString().padStart(2, "0")}`;
        },

        // 格式化时间显示（补零）
        formatTime(time) {
            return time.toString().padStart(2, "0");
        },

        // 继续下一轮
        continueNextRound() {
            if (!this.gameDetail.canContinue) {
                uni.showToast({
                    title: "无法继续下一轮",
                    icon: "none",
                });
                return;
            }

            // 进入下一轮
            this.gameDetail.currentRound++;
            this.gameDetail.pick = null; // 重置选择
            this.gameDetail.showRoundResult = false; // 隐藏结果页面
            this.gameDetail.canContinue = false;

            uni.showToast({
                title: `进入第${this.gameDetail.currentRound}轮`,
                icon: "success",
            });

            // 开始新一轮倒计时（会自动重置红蓝人数为0）
            this.startRoundCountdown();
        },

        // ========== 游戏相关方法 ==========

        // 用户报名
        signUp() {
            uni.showModal({
                title: "确认报名",
                content: "确定要报名参加这个游戏吗？",
                success: (res) => {
                    if (res.confirm) {
                        // TODO: 调用报名接口
                        this.gameDetail.isSignedUp = true;
                        this.gameDetail.signUpCount += 1;

                        uni.showToast({
                            title: "报名成功！",
                            icon: "success",
                        });
                    }
                },
            });
        },

        // 用户选择红方或蓝方
        makeChoice(choice) {
            // 检查游戏状态
            if (this.gameDetail.phase !== 'round' || this.gameDetail.userStatus !== 'playing') {
                uni.showToast({
                    title: "当前无法选择",
                    icon: "none",
                });
                return;
            }

            // 检查是否已经选择过
            if (this.gameDetail.pick !== null) {
                uni.showToast({
                    title: "您已经做过选择了",
                    icon: "none",
                });
                return;
            }

            // 检查倒计时是否结束
            if (this.gameDetail.roundCountdown <= 0) {
                uni.showToast({
                    title: "选择时间已结束",
                    icon: "none",
                });
                return;
            }

            const choiceName = choice === 0 ? "红方" : "蓝方";

            uni.showModal({
                title: "确认选择",
                content: `确定选择${choiceName}吗？\n选择后不能修改`,
                success: (res) => {
                    if (res.confirm) {
                        // 记录用户选择，但不立即显示人数
                        this.gameDetail.pick = choice;

                        uni.showToast({
                            title: `已选择${choiceName}，等待倒计时结束...`,
                            icon: "success",
                        });
                    }
                },
            });
        },

        // 计算选择按钮的宽度比例
        getChoiceWidth(choice) {
            const total = this.gameDetail.redNum + this.gameDetail.blueNum;
            if (total === 0) return "50%";

            const count = choice === 0 ? this.gameDetail.redNum : this.gameDetail.blueNum;
            const percentage = Math.max((count / total) * 100, 10); // 最小宽度10%
            return `${percentage.toFixed(0)}%`;
        },

        // 查看获奖用户
        viewWinners() {
            // TODO: 跳转到获奖用户页面
            uni.navigateTo({
                url: `/pages/games/pages/winners?gameId=${this.gameDetail.id}`,
            });
        },

        // 领取奖品
        claimPrize() {
            uni.showModal({
                title: "领取奖品",
                content: "请联系客服领取您的奖品，或填写收货地址。",
                confirmText: "联系客服",
                cancelText: "填写地址",
                success: (res) => {
                    if (res.confirm) {
                        // TODO: 联系客服逻辑
                        uni.showToast({
                            title: "正在为您转接客服...",
                            icon: "none",
                        });
                    } else if (res.cancel) {
                        // TODO: 跳转到地址填写页面
                        uni.navigateTo({
                            url: `/pages/games/pages/address?gameId=${this.gameDetail.id}`,
                        });
                    }
                },
            });
        },

        // 分享游戏
        shareGame() {
            // TODO: 实现分享功能
            uni.showActionSheet({
                itemList: ["分享给好友", "分享到朋友圈", "复制链接"],
                success: (res) => {
                    switch (res.tapIndex) {
                        case 0:
                            uni.showToast({
                                title: "分享给好友",
                                icon: "none",
                            });
                            break;
                        case 1:
                            uni.showToast({
                                title: "分享到朋友圈",
                                icon: "none",
                            });
                            break;
                        case 2:
                            uni.setClipboardData({
                                data: `https://example.com/game/${this.gameDetail.id}`,
                                success: () => {
                                    uni.showToast({
                                        title: "链接已复制",
                                        icon: "success",
                                    });
                                },
                            });
                            break;
                    }
                },
            });
        },

        // ========== 弹幕相关方法 ==========

        // 获取弹幕列表
        async getBarrageList(isInit) {
            try {
                // TODO: 替换为实际的API调用
                // let res = await getBarrageListApi(this.danmuContion)
                // let resData = (res && res.data) || {}
                // let list = Array.isArray(resData.records) ? resData.records : []

                // 模拟API数据
                let list = [];
                for (let i = 0; i < 20; i++) {
                    list.push({
                        nickname: `用户${i + 1}`,
                        voteName: this.gameDetail.title || "游戏",
                        avatarUrl: `https://picsum.photos/44/44?random=${i}`,
                        content: `用户${i + 1} 已为《${this.gameDetail.title || "游戏"}》投下宝贵的一票`,
                    });
                }

                // 处理弹幕数据
                list.map((item) => {
                    item.color = "#000000"; // 黑色文字
                    item.timestampt = new Date().getTime();
                    // 不添加图片，只保留文字内容
                    item.content = `${item.nickname} 已为《${item.voteName}》投下宝贵的一票`;
                });

                let danmuLength = this.danmuList.length;
                this.danmuList = list;
                this.addBarrage(isInit || danmuLength === 0);
            } catch (e) {
                console.error("查询弹幕列表失败:", e);
                uni.showToast({
                    title: (e && e.message) || "查询弹幕列表失败",
                    icon: "none",
                    duration: 2000,
                });
            }
        },

        // 添加弹幕到组件
        addBarrage(isInit) {
            if (!isInit || !this.danmuList.length) {
                return;
            }

            const barrageComp = this.$refs && this.$refs.bulletComment;
            if (barrageComp) {
                barrageComp.getBarrageInstance({
                    duration: 15, // 弹幕动画时长
                    lineHeight: 1.8, // 弹幕行高，更紧凑
                    padding: [5, 5, 5, 5], // 弹幕区四周留白
                    alpha: 1, // 全局透明度
                    font: "10px PingFang SC", // 全局字体
                    range: [0, 1], // 弹幕显示的垂直范围
                    tunnelShow: false, // 不显示轨道线
                    tunnelMaxNum: 100, // 隧道最大缓冲长度
                    maxLength: 50, // 弹幕最大字节长度
                    safeGap: 10, // 发送时的安全间隔
                    enableTap: false, // 不允许点击弹幕
                    danmuList: this.danmuList,
                });
            }
        },

        // 初始化弹幕组件
        initBulletComment() {
            this.$nextTick(() => {
                // 获取弹幕数据并初始化
                this.getBarrageList(true);
            });
        },

        // 加载初始弹幕数据（模拟数据）
        loadInitialDanmu() {
            const initialDanmu = [];

            // 生成15条初始弹幕
            for (let i = 0; i < 15; i++) {
                initialDanmu.push({
                    content: "这是弹幕",
                    color: "#000000", // 黑色文字，不要图片
                });
            }

            // 添加到弹幕列表
            this.danmuList = [...initialDanmu];

            // 分批随机加载弹幕，增加随机性
            this.loadDanmuRandomly(initialDanmu);
        },

        // 发送弹幕
        sendBulletComment() {
            if (!this.bulletComment.trim()) {
                uni.showToast({
                    title: "请输入弹幕内容",
                    icon: "none",
                });
                return;
            }

            // 构造弹幕数据
            const newDanmu = {
                content: this.bulletComment.trim(),
                color: "#000000", // 黑色文字，不要图片
            };

            // 本地发送弹幕
            this.sendDanmuLocal(newDanmu);

            // 预留接口调用位置
            // this.sendDanmuToServer(newDanmu);

            // 清空输入框
            this.bulletComment = "";
        },

        // 本地发送弹幕
        sendDanmuLocal(danmuData) {
            try {
                // 添加到本地弹幕列表
                this.danmuList.push(danmuData);

                // 发送到弹幕组件
                if (this.$refs.bulletComment) {
                    this.$refs.bulletComment.addData([danmuData]);
                }
            } catch (error) {
                console.error("弹幕发送失败:", error);
                uni.showToast({
                    title: "弹幕发送失败",
                    icon: "none",
                });
            }
        },

        // 预留：发送弹幕到服务器
        async sendDanmuToServer(danmuData) {
            try {
            } catch (error) {
                console.error("发送弹幕到服务器失败:", error);
                // 可以在这里处理错误，比如显示错误提示
            }
        },

        // 弹幕重新加载回调
        async reloadDanmu(type) {
            const currentTime = Date.now();

            // 检查冷却时间，避免频繁重载
            if (currentTime - this.lastReloadTime < this.reloadCooldown) {
                return;
            }
            this.lastReloadTime = currentTime;

            const barrageComp = this.$refs && this.$refs.bulletComment;
            if (type === "addDanmu") {
                // 继续添加更多弹幕
                await this.getBarrageList(false);
                if (barrageComp) {
                    barrageComp.open();
                    barrageComp.addData(this.danmuList);
                }
            } else {
                // 重新初始化弹幕，加载完整的弹幕数据
                await this.getBarrageList(true);
            }
        },

        // 预留：从服务器获取弹幕数据
        async getDanmuFromServer() {
            try {
                // TODO: 调用后端接口获取弹幕
                const response = await uni.request({
                    url: "/api/game/danmu/list",
                    method: "GET",
                    data: {
                        gameId: this.gameDetail.id,
                        page: 1,
                        limit: 10,
                    },
                });

                if (response.data.code === 200) {
                    const danmuList = response.data.data || [];
                    if (this.$refs.bulletComment && danmuList.length > 0) {
                        this.$refs.bulletComment.addData(danmuList);
                    }
                }
            } catch (error) {
                console.error("获取弹幕数据失败:", error);
            }
        },

        // 获取随机颜色
        getRandomColor() {
            const colors = [
                "#FF6B6B",
                "#4ECDC4",
                "#45B7D1",
                "#96CEB4",
                "#FFEAA7",
                "#DDA0DD",
                "#98D8C8",
                "#F7DC6F",
                "#BB8FCE",
                "#85C1E9",
                "#F8C471",
                "#82E0AA",
            ];
            return colors[Math.floor(Math.random() * colors.length)];
        },

        // ========== 测试方法（开发阶段使用） ==========

        // 测试切换游戏阶段
        testChangePhase(phase) {
            this.clearCountdown();
            this.gameDetail.phase = phase;
            this.gameDetail.showRoundResult = false; // 重置结果显示

            if (phase === 'signup') {
                this.gameDetail.userStatus = null;
                this.gameDetail.pick = null;
                this.gameDetail.isSignedUp = false;
                this.gameDetail.redNum = 0;
                this.gameDetail.blueNum = 0;
                this.gameDetail.signUpCountdown = this.gameConfig.signUpDuration;
                this.startSignUpCountdown();
            } else if (phase === 'round') {
                this.gameDetail.userStatus = 'playing';
                this.gameDetail.pick = null;
                this.gameDetail.isSignedUp = true;
                this.gameDetail.roundCountdown = this.gameConfig.roundDuration;
                this.startRoundCountdown(); // 会自动重置红蓝人数为0
            }

            uni.showToast({
                title: `切换到${phase === 'signup' ? '报名' : '轮次'}阶段`,
                icon: "none",
            });
        },

        // 测试切换用户状态
        testChangeStatus(status) {
            this.gameDetail.userStatus = status;

            if (status === 'eliminated') {
                this.clearCountdown();
            } else if (status === 'winner') {
                this.clearCountdown();
                this.gameDetail.phase = 'finished';
                this.gameDetail.winners = [{
                    userId: 'test_user',
                    nickname: this.userInfo.nickname,
                    round: this.gameDetail.currentRound
                }];
            }

            const statusNames = {
                'playing': "游戏中",
                'eliminated': "已淘汰",
                'winner': "获奖",
            };

            uni.showToast({
                title: `切换到: ${statusNames[status]}`,
                icon: "none",
            });
        },

        // 测试重置游戏
        testResetGame() {
            this.clearCountdown();
            this.gameDetail.phase = 'signup';
            this.gameDetail.userStatus = null;
            this.gameDetail.pick = null;
            this.gameDetail.isSignedUp = false;
            this.gameDetail.currentRound = 1;
            this.gameDetail.signUpCount = 0;
            this.gameDetail.redNum = 0;
            this.gameDetail.blueNum = 0;
            this.gameDetail.eliminatedCount = 0;
            this.gameDetail.remainingCount = 0;
            this.gameDetail.winners = [];
            this.gameDetail.showRoundResult = false;
            this.gameDetail.canContinue = false;
            this.gameDetail.signUpCountdown = this.gameConfig.signUpDuration;

            this.startSignUpCountdown();

            uni.showToast({
                title: "游戏已重置",
                icon: "success",
            });
        },

        // 测试下一轮
        testNextRound() {
            if (this.gameDetail.phase !== 'round') {
                uni.showToast({
                    title: "当前不在轮次阶段",
                    icon: "none",
                });
                return;
            }

            this.gameDetail.currentRound++;
            this.gameDetail.pick = null;
            this.gameDetail.userStatus = 'playing';
            this.startRoundCountdown();

            uni.showToast({
                title: `进入第${this.gameDetail.currentRound}轮`,
                icon: "success",
            });
        },

        // 测试报名
        testSignUp() {
            if (this.gameDetail.phase !== 'signup') {
                uni.showToast({
                    title: "当前不在报名阶段",
                    icon: "none",
                });
                return;
            }

            this.gameDetail.isSignedUp = true;
            this.gameDetail.signUpCount += 1;

            uni.showToast({
                title: "模拟报名成功",
                icon: "success",
            });
        },

        // 测试强制结束轮次
        testForceRoundEnd() {
            if (this.gameDetail.phase !== 'round') {
                uni.showToast({
                    title: "当前不在轮次阶段",
                    icon: "none",
                });
                return;
            }

            this.clearCountdown();
            this.onRoundEnd();

            uni.showToast({
                title: "强制结束轮次",
                icon: "success",
            });
        },

        // 测试显示轮次结果
        testShowRoundResult() {
            this.gameDetail.showRoundResult = true;
            this.gameDetail.canContinue = true;
            this.gameDetail.pick = Math.floor(Math.random() * 2); // 随机选择
            this.gameDetail.redNum = Math.floor(Math.random() * 50) + 20;
            this.gameDetail.blueNum = Math.floor(Math.random() * 50) + 20;

            uni.showToast({
                title: "显示轮次结果",
                icon: "success",
            });
        },
    },
};
</script>

<style lang="scss" scoped>
.view {
    .content {
        padding: 24rpx 32rpx;
        padding-bottom: calc(env(safe-area-inset-bottom) + 20rpx);
        .header {
            margin-top: 24rpx;
        }
        .prize {
            margin-top: 32rpx;
            .prize-item {
                margin-top: 24rpx;
                padding: 12rpx;
                border-radius: 12rpx;
                background: #f7f8fa;
            }
        }
        .game-info {
            border-radius: 8rpx;
            background: #1d7bf750;
            color: #fff;
            margin-top: 24rpx;
            .item {
                flex: 1;
                padding: 12rpx;

                &.active {
                    padding: 12rpx;
                    border-radius: 8rpx;
                    background: #1d7bf7;
                }
                .lable {
                    font-size: 24rpx;
                }
                .value {
                    font-size: 24rpx;
                }
            }
        }
        .game-status {
            margin-top: 40rpx;
        }

        // 报名按钮样式
        .signup-container {
            margin-top: 32rpx;

            .signup-btn {
                height: 88rpx;
                background: linear-gradient(90deg, #1d7bf7 0%, #0435ff 100%);
                border-radius: 44rpx;

                &:active {
                    opacity: 0.8;
                }
            }

            .signed-up {
                height: 88rpx;
                background: #f0f9ff;
                border: 2rpx solid #67c23a;
                border-radius: 44rpx;
            }
        }

        // 轮次结果展示样式
        .round-result-container {
            margin-top: 32rpx;
            padding: 24rpx;
            background: #f8f9fa;
            border-radius: 16rpx;

            .result-title {
                margin-bottom: 16rpx;
            }

            .result-subtitle {
                margin-bottom: 24rpx;
            }

            .continue-btn-container {
                margin-top: 24rpx;
            }
        }

        .game-choice-container {
            margin-top: 24rpx;

            // 未选择状态 - 1:1比例
            .game-choice {
                display: flex;
                align-items: center;

                .choice-btn {
                    flex: 1; // 1:1比例
                    height: 80rpx;
                    transition: all 0.3s ease;

                    .choice-text {
                        font-weight: bold;
                    }

                    &.left {
                        border-radius: 16rpx 0 0 16rpx;
                        background: linear-gradient(90deg, #f71914 0%, #f5582b 100%);
                    }

                    &.right {
                        border-radius: 0 16rpx 16rpx 0;
                        background: linear-gradient(90deg, #2171fe 0%, #0435ff 100%);
                    }

                    // 点击效果
                    &:active {
                        transform: scale(0.98);
                        opacity: 0.8;
                    }
                }
            }

            // 已选择状态 - 按比例显示
            .choice-result {
                display: flex;
                height: 80rpx;
                overflow: hidden;

                .result-btn {
                    height: 100%;
                    transition: all 0.3s ease;
                    position: relative;

                    .choice-text {
                        font-weight: bold;
                        margin-bottom: 4rpx;
                    }

                    .choice-count {
                        font-size: 20rpx;
                        opacity: 0.9;
                    }

                    &.left {
                        border-radius: 16rpx 0 0 16rpx;
                        background: linear-gradient(90deg, #f71914 0%, #f5582b 100%);
                    }

                    &.right {
                        border-radius: 0 16rpx 16rpx 0;
                        background: linear-gradient(90deg, #2171fe 0%, #0435ff 100%);
                    }

                    // 用户选择的高亮效果
                    &.user-selected {
                        position: relative;

                        &::after {
                            content: "✓";
                            position: absolute;
                            top: 8rpx;
                            right: 12rpx;
                            color: #fff;
                            font-size: 24rpx;
                            font-weight: bold;
                        }
                    }
                }
            }
        }
        // 倒计时样式
        .countdown-container {
            margin-top: 22rpx;
            .countdown-time {
                .time-item {
                    background: #f50c0c;
                    border-radius: 8rpx;
                    padding: 8rpx;
                    display: flex;
                    align-items: center;
                    justify-content: center;

                    .time-num {
                        font-size: 24rpx;
                        font-weight: bold;
                        color: #fff;
                        line-height: 1;
                    }
                }

                .time-separator {
                    font-size: 24rpx;
                    font-weight: bold;
                    color: #f50c0c;
                    line-height: 1;
                }
            }
        }

        .btn-container {
            margin-top: 56rpx;

            .btn {
                width: 100%;
            }
        }
        .bullet-comment {
            width: 100%;
            height: 350rpx;
            margin-top: 40rpx;
            .bullet-content {
                width: 100%;
                height: 350rpx;
                box-sizing: border-box;
            }
        }
        .send-message {
            margin-top: 40rpx;
        }
    }
}
</style>
