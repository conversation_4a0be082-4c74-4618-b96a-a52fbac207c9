<template>
    <view class="view">
        <section class="nav-bar">
            <c-navBar title="游戏详情" isShadow isBack isPerch> </c-navBar>
        </section>
        <section class="content">
            <view class="swiper">
                <u-swiper :list="list" indicator indicatorMode="dot" height="320rpx" circular></u-swiper>
            </view>
            <view class="header">
                <view class="flex align-center justify-between">
                    <text class="mf-font-28 mf-weight-bold" style="color: #191919">{{ gameDetail.title }}</text>
                    <u-icon name="share-square" bold color="#191919" size="28rpx" @click="shareGame"></u-icon>
                </view>
                <view class="flex align-center justify-between" style="margin-top: 20rpx">
                    <text class="mf-font-28" style="color: #333333">开始时间：{{ gameDetail.startTime }}</text>
                    <view class="flex align-center gap-8" @click="$fn.jumpPage('/pages/tabbar/parse?title=游戏规则')">
                        <u-icon
                            name="error-circle"
                            color="#1D7BF7"
                            label="游戏规则"
                            label-color="#1d7bf7"
                            label-size="24rpx"
                            :top="1"
                            size="24rpx"
                        ></u-icon>
                    </view>
                </view>
            </view>
            <view class="prize">
                <text class="mf-font-28 mf-weight-bold" style="color: #191919">活动礼品</text>
                <view class="prize-item flex align-center gap-20">
                    <u-image
                        src="https://picsum.photos/96/96?random=1"
                        radius="6rpx"
                        width="96rpx"
                        height="96rpx"
                        mode="aspectFill"
                    ></u-image>
                    <view class="flex flex-col gap-20">
                        <text class="prize-title mf-font-28" style="color: #070f1a">{{ gameDetail.prize }}</text>
                        <text class="prize-desc mf-font-20" style="color: #606266">礼品数量：{{ gameDetail.prizeCount }}台</text>
                    </view>
                </view>
            </view>
            <!-- 游戏信息 -->
            <view class="game-info flex align-center">
                <view class="item flex flex-col flex-center">
                    <text class="label">报名人数</text>
                    <text class="value">{{ gameDetail.signUpCount }}</text>
                </view>
                <view class="item flex flex-col flex-center active">
                    <text class="label">当前轮次</text>
                    <text class="value">第{{ gameDetail.currentRound }}轮</text>
                </view>
                <view class="item flex flex-col flex-center">
                    <text class="label">已淘汰</text>
                    <text class="value">{{ gameDetail.eliminatedCount }}</text>
                </view>
                <view class="item flex flex-col flex-center">
                    <text class="label">剩余</text>
                    <text class="value">{{ gameDetail.remainingCount }}</text>
                </view>
            </view>
            <!-- 游戏状态 -->
            <view class="game-status">
                <!-- 状态1: 准备中 - 等待用户选择 -->
                <view class="flex flex-col flex-center gap-8" v-if="gameDetail.status === 0 && gameDetail.userStatus === null">
                    <text class="mf-font-32 mf-weight-bold" style="color: #191919">第{{ gameDetail.currentRound }}轮倒计时</text>
                    <text class="mf-font-24" style="color: #191919">请选择红方或蓝方</text>
                </view>

                <!-- 状态2: 晋级 - 用户晋级下一轮 -->
                <view class="flex flex-col flex-center gap-8" v-if="gameDetail.userStatus === 1">
                    <text class="mf-font-32 mf-weight-bold" style="color: #191919">您已晋级第{{ gameDetail.currentRound + 1 }}轮</text>
                    <text class="mf-font-24" style="color: #191919">下一轮即将开始</text>
                </view>

                <!-- 状态3: 淘汰 - 用户被淘汰 -->
                <view class="flex flex-col flex-center gap-8" v-if="gameDetail.userStatus === 2">
                    <text class="mf-font-32 mf-weight-bold" style="color: #191919">您已被淘汰</text>
                    <text class="mf-font-24" style="color: #191919">别灰心！下次再参与</text>
                </view>

                <!-- 状态4: 获奖 - 用户获奖 -->
                <view class="flex flex-col flex-center gap-8" v-if="gameDetail.userStatus === 3">
                    <text class="mf-font-32 mf-weight-bold" style="color: #191919">恭喜您获奖了！</text>
                    <text class="mf-font-24" style="color: #191919">快去领取您的奖品吧！</text>
                </view>
            </view>
            <!-- 游戏选择区域 -->
            <view class="game-choice-container" v-if="gameDetail.status === 0">
                <!-- 选择按钮 - 只在用户未选择时显示 -->
                <view class="game-choice flex align-center" v-if="gameDetail.userStatus === null">
                    <view
                        class="choice-btn left flex flex-col flex-center"
                        :style="{ width: getChoiceWidth(0) }"
                        @click="makeChoice(0)"
                    >
                        <text class="choice-text mf-font-28" style="color: #fff">红方</text>
                        <text class="choice-count mf-font-20" style="color: #fff">{{ gameDetail.redNum }}人</text>
                    </view>
                    <view
                        class="choice-btn right flex flex-col flex-center"
                        :style="{ width: getChoiceWidth(1) }"
                        @click="makeChoice(1)"
                    >
                        <text class="choice-text mf-font-28" style="color: #fff">蓝方</text>
                        <text class="choice-count mf-font-20" style="color: #fff">{{ gameDetail.blueNum }}人</text>
                    </view>
                </view>

                <!-- 已选择状态显示 -->
                <view class="choice-result flex align-center" v-if="gameDetail.userStatus !== null">
                    <view
                        class="result-btn left flex flex-col flex-center"
                        :style="{ width: getChoiceWidth(0) }"
                        :class="{ 'user-selected': gameDetail.pick === 0 }"
                    >
                        <text class="choice-text mf-font-28" style="color: #fff">红方</text>
                        <text class="choice-count mf-font-20" style="color: #fff">{{ gameDetail.redNum }}人</text>
                    </view>
                    <view
                        class="result-btn right flex flex-col flex-center"
                        :style="{ width: getChoiceWidth(1) }"
                        :class="{ 'user-selected': gameDetail.pick === 1 }"
                    >
                        <text class="choice-text mf-font-28" style="color: #fff">蓝方</text>
                        <text class="choice-count mf-font-20" style="color: #fff">{{ gameDetail.blueNum }}人</text>
                    </view>
                </view>
            </view>
            <!-- 倒计时显示 -->
            <view class="countdown-container flex align-center justify-center gap-12" v-if="gameDetail.status === 1">
                <text class="mf-font-24" style="color: #070f1a">开始倒计时</text>
                <view class="countdown-time flex align-center gap-8">
                    <view class="time-item">
                        <text class="time-num">{{ formatTime(gameDetail.countdown.hours) }}</text>
                    </view>
                    <text class="time-separator">:</text>
                    <view class="time-item">
                        <text class="time-num">{{ formatTime(gameDetail.countdown.minutes) }}</text>
                    </view>
                    <text class="time-separator">:</text>
                    <view class="time-item">
                        <text class="time-num">{{ formatTime(gameDetail.countdown.seconds) }}</text>
                    </view>
                </view>
            </view>
            <!-- 操作按钮 -->
            <view class="btn-container">
                <!-- 报名按钮 - 游戏待开始且用户未报名 -->
                <view class="btn flex align-center justify-center" v-if="gameDetail.status === 1 && !gameDetail.isSignedUp">
                    <u-button
                        type="primary"
                        size="large"
                        :custom-style="{
                            backgroundColor: '#1D7BF7',
                            borderColor: '#1D7BF7',
                            borderRadius: '32rpx',
                            width: '100%',
                            height: '64rpx',
                        }"
                        @click="signUp"
                    >
                        <view class="flex align-center gap-12">
                            <text class="mf-font-24" style="color: #fff">立即报名</text>
                        </view>
                    </u-button>
                </view>

                <!-- 已报名状态 -->
                <view class="btn flex align-center justify-center" v-if="gameDetail.status === 1 && gameDetail.isSignedUp">
                    <u-button
                        type="primary"
                        size="large"
                        :custom-style="{
                            backgroundColor: '#999',
                            borderColor: '#999',
                            borderRadius: '32rpx',
                            width: '100%',
                            height: '64rpx',
                        }"
                        disabled
                    >
                        <view class="flex align-center gap-12">
                            <text class="mf-font-24" style="color: #fff">已报名</text>
                        </view>
                    </u-button>
                </view>

                <!-- 双按钮布局 - 查看获奖用户和领取奖品 -->
                <view class="btn flex align-center justify-between gap-24" v-if="gameDetail.userStatus === 3">
                    <u-button
                        type="plain"
                        size="large"
                        :custom-style="{
                            backgroundColor: 'transparent',
                            borderColor: '#1D7BF7',
                            borderRadius: '32rpx',
                            width: '48%',
                            height: '64rpx',
                        }"
                        @click="viewWinners"
                    >
                        <view class="flex align-center gap-12">
                            <text class="mf-font-24" style="color: #1D7BF7">获奖用户</text>
                        </view>
                    </u-button>
                    <u-button
                        type="primary"
                        size="large"
                        :custom-style="{
                            backgroundColor: '#1D7BF7',
                            borderColor: '#1D7BF7',
                            borderRadius: '32rpx',
                            width: '48%',
                            height: '64rpx',
                        }"
                        @click="claimPrize"
                    >
                        <view class="flex align-center gap-12">
                            <text class="mf-font-24" style="color: #fff">领取奖品</text>
                        </view>
                    </u-button>
                </view>

                <!-- 查看获奖用户按钮 - 其他状态 -->
                <view class="btn flex align-center justify-center" v-if="gameDetail.userStatus !== 3 && gameDetail.status !== 1">
                    <u-button
                        type="plain"
                        size="large"
                        :custom-style="{
                            backgroundColor: 'transparent',
                            borderColor: '#1D7BF7',
                            borderRadius: '32rpx',
                            width: '100%',
                            height: '64rpx',
                        }"
                        @click="viewWinners"
                    >
                        <view class="flex align-center gap-12">
                            <text class="mf-font-24" style="color: #1D7BF7">查看获奖用户</text>
                        </view>
                    </u-button>
                </view>
            </view>
            <view class="bullet-comment">
                <c-bulletComment ref="bulletComment" class="bullet-content" @reloadDanmu="reloadDanmu"></c-bulletComment>
            </view>
            <view class="send-message">
                <u-input
                    v-model="bulletComment"
                    placeholder="说点什么..."
                    :customStyle="{
                        borderRadius: '36rpx',
                        height: '72rpx',
                        background: '#F5F6F7',
                        padding: '0 24rpx',
                    }"
                    border="none"
                    clearable
                    confirmType="send"
                    @confirm="sendBulletComment"
                />
            </view>

            <!-- 测试按钮 - 开发阶段使用 -->
            <view class="test-buttons" style="margin-top: 40rpx; padding: 20rpx; background: #f0f0f0; border-radius: 12rpx;">
                <text style="font-size: 24rpx; color: #666; margin-bottom: 20rpx;">测试状态切换：</text>
                <view class="flex align-center gap-12" style="flex-wrap: wrap;">
                    <u-button size="mini" type="primary" @click="switchToStatus(null)">准备中</u-button>
                    <u-button size="mini" type="success" @click="switchToStatus(1)">晋级</u-button>
                    <u-button size="mini" type="error" @click="switchToStatus(2)">淘汰</u-button>
                    <u-button size="mini" type="warning" @click="switchToStatus(3)">获奖</u-button>
                </view>
            </view>
        </section>
    </view>
</template>

<script>
export default {
    data() {
        return {
            list: [
                "https://picsum.photos/750/750?random=1",
                "https://picsum.photos/750/750?random=2",
                "https://picsum.photos/750/750?random=3",
                "https://picsum.photos/750/750?random=4",
            ],
            // 游戏详情数据
            gameDetail: {
                id: 1,
                title: "庆祝门店十周年庆活动，满减券各种福利做游戏免费领",
                startTime: "2025-07-30 20:30:00", // 游戏开始时间
                status: 0, // 游戏状态 0: 进行中 1: 待开始 2: 已结束
                prize: "苹果16pro max 256GB*1",
                prizeCount: 3,
                signUpCount: 300, // 报名人数
                isSignedUp: true, // 用户是否已报名
                currentRound: 2, // 当前轮次
                nextRoundTime: "2025-07-30 21:00:00", // 下一轮开始时间
                roundCountdown: 0, // 轮次倒计时（秒）
                countdown: {
                    hours: 0,
                    minutes: 0,
                    seconds: 0,
                },
                userStatus: null, // null-未选择，1-晋级，2-淘汰，3-获奖
                pick: null, // 0-红方，1-蓝方
                redNum: 120, // 红方人数
                blueNum: 80, // 蓝方人数
                eliminatedCount: 100, // 已淘汰人数
                remainingCount: 200, // 剩余人数
            },
            totalSeconds: 0, // 淘汰-下一轮倒计时-秒
            countdownTimer: null, // 倒计时定时器
            bulletComment: "", // 弹幕相关
            danmuList: [], // 弹幕数据列表
            danmuContion: {
                // 弹幕查询条件
                page: 1,
                size: 200,
            },
            userInfo: {
                nickname: "游客",
                avatar: "https://picsum.photos/60/60?random=1",
            },
            // 弹幕重载控制
            lastReloadTime: 0, // 上次重载时间
            reloadCooldown: 5000, // 重载冷却时间 5秒
        };
    },
    onLoad(options) {
        // 获取游戏ID
        if (options.id) {
            this.gameDetail.id = options.id;
        }
        // 初始化倒计时
        this.initCountdown();
        // 初始化弹幕
        this.initBulletComment();
    },
    onShow() {
        // 页面显示时重新初始化倒计时
        this.initCountdown();
    },
    onHide() {
        // 页面隐藏时清除定时器
        this.clearCountdown();
    },
    onUnload() {
        // 页面卸载时清除定时器
        this.clearCountdown();
    },
    methods: {
        // 初始化倒计时
        initCountdown() {
            // 只有状态为1（待开始）的游戏才需要倒计时
            if (this.gameDetail.status !== 1) {
                return;
            }

            // 解析开始时间
            const startTime = new Date(this.gameDetail.startTime).getTime();
            const currentTime = new Date().getTime();

            // 只有开始时间大于当前时间才开始倒计时
            if (startTime <= currentTime) {
                // 如果开始时间已过，更新状态为进行中
                this.gameDetail.status = 0;
                return;
            }

            // 计算剩余秒数
            const remainingSeconds = Math.floor((startTime - currentTime) / 1000);
            this.totalSeconds = remainingSeconds;

            // 更新倒计时显示
            this.updateCountdownDisplay();

            // 开始倒计时
            this.startCountdown();
        },
        // 开始倒计时
        startCountdown() {
            // 清除之前的定时器
            this.clearCountdown();

            // 开始倒计时
            this.countdownTimer = setInterval(() => {
                if (this.totalSeconds > 0) {
                    this.totalSeconds--;
                    this.updateCountdownDisplay();
                } else {
                    // 倒计时结束，游戏开始
                    this.clearCountdown();
                    this.onCountdownEnd();
                }
            }, 1000);
        },
        // 更新倒计时显示
        updateCountdownDisplay() {
            const hours = Math.floor(this.totalSeconds / 3600);
            const minutes = Math.floor((this.totalSeconds % 3600) / 60);
            const seconds = this.totalSeconds % 60;

            this.gameDetail.countdown = {
                hours,
                minutes,
                seconds,
            };
        },
        // 清除倒计时
        clearCountdown() {
            if (this.countdownTimer) {
                clearInterval(this.countdownTimer);
                this.countdownTimer = null;
            }
        },
        // 倒计时结束处理
        onCountdownEnd() {
            // 更新游戏状态为进行中
            this.gameDetail.status = 0;

            uni.showToast({
                title: "游戏开始！",
                icon: "success",
            });
        },
        // 格式化时间显示（补零）
        formatTime(time) {
            return time.toString().padStart(2, "0");
        },

        // 格式化轮次倒计时
        formatRoundCountdown(seconds) {
            const minutes = Math.floor(seconds / 60);
            const remainingSeconds = seconds % 60;
            return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
        },

        // ========== 游戏相关方法 ==========

        // 用户报名
        signUp() {
            uni.showModal({
                title: '确认报名',
                content: '确定要报名参加这个游戏吗？',
                success: (res) => {
                    if (res.confirm) {
                        // TODO: 调用报名接口
                        this.gameDetail.isSignedUp = true;
                        this.gameDetail.signUpCount += 1;

                        uni.showToast({
                            title: '报名成功！',
                            icon: 'success'
                        });
                    }
                }
            });
        },

        // 用户选择红方或蓝方
        makeChoice(choice) {
            if (this.gameDetail.userStatus !== null) {
                uni.showToast({
                    title: '您已经做过选择了',
                    icon: 'none'
                });
                return;
            }

            const choiceName = choice === 0 ? '红方' : '蓝方';
            const choiceCount = choice === 0 ? this.gameDetail.redNum : this.gameDetail.blueNum;

            uni.showModal({
                title: '确认选择',
                content: `确定选择${choiceName}吗？\n当前${choiceName}有${choiceCount}人选择\n选择后不能修改`,
                success: (res) => {
                    if (res.confirm) {
                        // TODO: 调用选择接口
                        this.gameDetail.pick = choice;

                        // 更新对应方的人数
                        if (choice === 0) {
                            this.gameDetail.redNum += 1;
                        } else {
                            this.gameDetail.blueNum += 1;
                        }

                        // 模拟游戏结果，随机设置用户状态
                        setTimeout(() => {
                            const results = [1, 2, 3]; // 1-晋级, 2-淘汰, 3-获奖
                            const randomResult = results[Math.floor(Math.random() * results.length)];
                            this.gameDetail.userStatus = randomResult;

                            const statusText = {
                                1: '恭喜晋级！',
                                2: '很遗憾被淘汰',
                                3: '恭喜获奖！'
                            };

                            uni.showToast({
                                title: statusText[randomResult],
                                icon: randomResult === 2 ? 'none' : 'success',
                                duration: 2000
                            });
                        }, 2000);

                        uni.showToast({
                            title: `已选择${choiceName}，等待结果...`,
                            icon: 'success'
                        });
                    }
                }
            });
        },

        // 计算选择按钮的宽度比例
        getChoiceWidth(choice) {
            const total = this.gameDetail.redNum + this.gameDetail.blueNum;
            if (total === 0) return '50%';

            const count = choice === 0 ? this.gameDetail.redNum : this.gameDetail.blueNum;
            const percentage = Math.max((count / total) * 100, 10); // 最小宽度10%
            return `${percentage}%`;
        },

        // 查看获奖用户
        viewWinners() {
            // TODO: 跳转到获奖用户页面
            uni.navigateTo({
                url: `/pages/games/pages/winners?gameId=${this.gameDetail.id}`
            });
        },

        // 领取奖品
        claimPrize() {
            uni.showModal({
                title: '领取奖品',
                content: '请联系客服领取您的奖品，或填写收货地址。',
                confirmText: '联系客服',
                cancelText: '填写地址',
                success: (res) => {
                    if (res.confirm) {
                        // TODO: 联系客服逻辑
                        uni.showToast({
                            title: '正在为您转接客服...',
                            icon: 'none'
                        });
                    } else if (res.cancel) {
                        // TODO: 跳转到地址填写页面
                        uni.navigateTo({
                            url: `/pages/games/pages/address?gameId=${this.gameDetail.id}`
                        });
                    }
                }
            });
        },

        // 分享游戏
        shareGame() {
            // TODO: 实现分享功能
            uni.showActionSheet({
                itemList: ['分享给好友', '分享到朋友圈', '复制链接'],
                success: (res) => {
                    switch (res.tapIndex) {
                        case 0:
                            uni.showToast({
                                title: '分享给好友',
                                icon: 'none'
                            });
                            break;
                        case 1:
                            uni.showToast({
                                title: '分享到朋友圈',
                                icon: 'none'
                            });
                            break;
                        case 2:
                            uni.setClipboardData({
                                data: `https://example.com/game/${this.gameDetail.id}`,
                                success: () => {
                                    uni.showToast({
                                        title: '链接已复制',
                                        icon: 'success'
                                    });
                                }
                            });
                            break;
                    }
                }
            });
        },

        // ========== 弹幕相关方法 ==========

        // 获取弹幕列表
        async getBarrageList(isInit) {
            try {
                // TODO: 替换为实际的API调用
                // let res = await getBarrageListApi(this.danmuContion)
                // let resData = (res && res.data) || {}
                // let list = Array.isArray(resData.records) ? resData.records : []

                // 模拟API数据
                let list = [];
                for (let i = 0; i < 20; i++) {
                    list.push({
                        nickname: `用户${i + 1}`,
                        voteName: this.gameDetail.title || "游戏",
                        avatarUrl: `https://picsum.photos/44/44?random=${i}`,
                        content: `用户${i + 1} 已为《${this.gameDetail.title || "游戏"}》投下宝贵的一票`,
                    });
                }

                // 处理弹幕数据
                list.map((item) => {
                    item.color = "#000000"; // 黑色文字
                    item.timestampt = new Date().getTime();
                    // 不添加图片，只保留文字内容
                    item.content = `${item.nickname} 已为《${item.voteName}》投下宝贵的一票`;
                });

                let danmuLength = this.danmuList.length;
                this.danmuList = list;
                this.addBarrage(isInit || danmuLength === 0);
            } catch (e) {
                console.error("查询弹幕列表失败:", e);
                uni.showToast({
                    title: (e && e.message) || "查询弹幕列表失败",
                    icon: "none",
                    duration: 2000,
                });
            }
        },

        // 添加弹幕到组件
        addBarrage(isInit) {
            if (!isInit || !this.danmuList.length) {
                return;
            }

            const barrageComp = this.$refs && this.$refs.bulletComment;
            if (barrageComp) {
                barrageComp.getBarrageInstance({
                    duration: 15, // 弹幕动画时长
                    lineHeight: 1.8, // 弹幕行高，更紧凑
                    padding: [5, 5, 5, 5], // 弹幕区四周留白
                    alpha: 1, // 全局透明度
                    font: "10px PingFang SC", // 全局字体
                    range: [0, 1], // 弹幕显示的垂直范围
                    tunnelShow: false, // 不显示轨道线
                    tunnelMaxNum: 100, // 隧道最大缓冲长度
                    maxLength: 50, // 弹幕最大字节长度
                    safeGap: 10, // 发送时的安全间隔
                    enableTap: false, // 不允许点击弹幕
                    danmuList: this.danmuList,
                });
            }
        },

        // 初始化弹幕组件
        initBulletComment() {
            this.$nextTick(() => {
                // 获取弹幕数据并初始化
                this.getBarrageList(true);
            });
        },

        // 加载初始弹幕数据（模拟数据）
        loadInitialDanmu() {
            const initialDanmu = [];

            // 生成15条初始弹幕
            for (let i = 0; i < 15; i++) {
                initialDanmu.push({
                    content: "这是弹幕",
                    color: "#000000", // 黑色文字，不要图片
                });
            }

            // 添加到弹幕列表
            this.danmuList = [...initialDanmu];

            // 分批随机加载弹幕，增加随机性
            this.loadDanmuRandomly(initialDanmu);
        },

        // 发送弹幕
        sendBulletComment() {
            if (!this.bulletComment.trim()) {
                uni.showToast({
                    title: "请输入弹幕内容",
                    icon: "none",
                });
                return;
            }

            // 构造弹幕数据
            const newDanmu = {
                content: this.bulletComment.trim(),
                color: "#000000", // 黑色文字，不要图片
            };

            // 本地发送弹幕
            this.sendDanmuLocal(newDanmu);

            // 预留接口调用位置
            // this.sendDanmuToServer(newDanmu);

            // 清空输入框
            this.bulletComment = "";
        },

        // 本地发送弹幕
        sendDanmuLocal(danmuData) {
            try {
                // 添加到本地弹幕列表
                this.danmuList.push(danmuData);

                // 发送到弹幕组件
                if (this.$refs.bulletComment) {
                    this.$refs.bulletComment.addData([danmuData]);
                }
            } catch (error) {
                console.error("弹幕发送失败:", error);
                uni.showToast({
                    title: "弹幕发送失败",
                    icon: "none",
                });
            }
        },

        // 预留：发送弹幕到服务器
        async sendDanmuToServer(danmuData) {
            try {
            } catch (error) {
                console.error("发送弹幕到服务器失败:", error);
                // 可以在这里处理错误，比如显示错误提示
            }
        },

        // 弹幕重新加载回调
        async reloadDanmu(type) {
            const currentTime = Date.now();

            // 检查冷却时间，避免频繁重载
            if (currentTime - this.lastReloadTime < this.reloadCooldown) {
                return;
            }
            this.lastReloadTime = currentTime;

            const barrageComp = this.$refs && this.$refs.bulletComment;
            if (type === "addDanmu") {
                // 继续添加更多弹幕
                await this.getBarrageList(false);
                if (barrageComp) {
                    barrageComp.open();
                    barrageComp.addData(this.danmuList);
                }
            } else {
                // 重新初始化弹幕，加载完整的弹幕数据
                await this.getBarrageList(true);
            }
        },

        // 预留：从服务器获取弹幕数据
        async getDanmuFromServer() {
            try {
                // TODO: 调用后端接口获取弹幕
                const response = await uni.request({
                    url: "/api/game/danmu/list",
                    method: "GET",
                    data: {
                        gameId: this.gameDetail.id,
                        page: 1,
                        limit: 10,
                    },
                });

                if (response.data.code === 200) {
                    const danmuList = response.data.data || [];
                    if (this.$refs.bulletComment && danmuList.length > 0) {
                        this.$refs.bulletComment.addData(danmuList);
                    }
                }
            } catch (error) {
                console.error("获取弹幕数据失败:", error);
            }
        },

        // 获取随机颜色
        getRandomColor() {
            const colors = [
                "#FF6B6B",
                "#4ECDC4",
                "#45B7D1",
                "#96CEB4",
                "#FFEAA7",
                "#DDA0DD",
                "#98D8C8",
                "#F7DC6F",
                "#BB8FCE",
                "#85C1E9",
                "#F8C471",
                "#82E0AA",
            ];
            return colors[Math.floor(Math.random() * colors.length)];
        },

        // ========== 测试方法（开发阶段使用） ==========

        // 切换到指定状态
        switchToStatus(status) {
            this.gameDetail.userStatus = status;

            // 如果切换到准备中状态，重置选择
            if (status === null) {
                this.gameDetail.pick = null;
            } else if (status !== null && this.gameDetail.pick === null) {
                // 如果有状态但没有选择，随机设置一个选择
                this.gameDetail.pick = Math.floor(Math.random() * 2);
            }

            const statusNames = {
                null: '准备中',
                1: '晋级',
                2: '淘汰',
                3: '获奖'
            };

            uni.showToast({
                title: `切换到: ${statusNames[status]}`,
                icon: 'none'
            });
        },

        // ========== 测试方法（开发阶段使用） ==========

        // 切换游戏状态（用于测试不同状态的显示效果）
        switchGameStatus() {
            const statuses = [
                { status: 1, userStatus: null, isSignedUp: false, desc: '待开始-未报名' },
                { status: 1, userStatus: null, isSignedUp: true, desc: '待开始-已报名' },
                { status: 0, userStatus: null, isSignedUp: true, desc: '进行中-未选择' },
                { status: 0, userStatus: 0, pick: 0, desc: '进行中-已选择红方' },
                { status: 0, userStatus: 1, desc: '用户晋级' },
                { status: 0, userStatus: 2, desc: '用户被淘汰' },
                { status: 0, userStatus: 3, desc: '用户获奖' },
            ];

            // 随机选择一个状态
            const randomStatus = statuses[Math.floor(Math.random() * statuses.length)];

            Object.assign(this.gameDetail, randomStatus);

            console.log('切换到状态:', randomStatus.desc);
            uni.showToast({
                title: `切换到: ${randomStatus.desc}`,
                icon: 'none'
            });
        },
    },
};
</script>

<style lang="scss" scoped>
.view {
    .content {
        padding: 24rpx 32rpx;
        padding-bottom: calc(env(safe-area-inset-bottom) + 20rpx);
        .header {
            margin-top: 24rpx;
        }
        .prize {
            margin-top: 32rpx;
            .prize-item {
                margin-top: 24rpx;
                padding: 12rpx;
                border-radius: 12rpx;
                background: #f7f8fa;
            }
        }
        .game-info {
            border-radius: 8rpx;
            background: #1d7bf750;
            color: #fff;
            margin-top: 24rpx;
            .item {
                flex: 1;
                padding: 12rpx;

                &.active {
                    padding: 12rpx;
                    border-radius: 8rpx;
                    background: #1d7bf7;
                }
                .lable {
                    font-size: 24rpx;
                }
                .value {
                    font-size: 24rpx;
                }
            }
        }
        .game-status {
            margin-top: 40rpx;
        }
        .game-choice-container {
            margin-top: 24rpx;

            .game-choice, .choice-result {
                display: flex;
                height: 80rpx;
                border-radius: 16rpx;
                overflow: hidden;

                .choice-btn, .result-btn {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    height: 100%;
                    transition: all 0.3s ease;
                    position: relative;

                    .choice-text {
                        font-weight: bold;
                        margin-bottom: 4rpx;
                    }

                    .choice-count {
                        font-size: 20rpx;
                        opacity: 0.9;
                    }

                    &.left {
                        background: linear-gradient(90deg, #f71914 0%, #f5582b 100%);
                    }

                    &.right {
                        background: linear-gradient(90deg, #2171fe 0%, #0435ff 100%);
                    }

                    // 用户选择的高亮效果
                    &.user-selected {
                        position: relative;

                        &::after {
                            content: '✓';
                            position: absolute;
                            top: 8rpx;
                            right: 12rpx;
                            color: #fff;
                            font-size: 24rpx;
                            font-weight: bold;
                        }
                    }
                }

                // 点击效果
                .choice-btn:active {
                    transform: scale(0.98);
                    opacity: 0.8;
                }
            }
        }
        // 倒计时样式
        .countdown-container {
            margin-top: 22rpx;
            .countdown-time {
                .time-item {
                    background: #f50c0c;
                    border-radius: 8rpx;
                    padding: 8rpx;
                    display: flex;
                    align-items: center;
                    justify-content: center;

                    .time-num {
                        font-size: 24rpx;
                        font-weight: bold;
                        color: #fff;
                        line-height: 1;
                    }
                }

                .time-separator {
                    font-size: 24rpx;
                    font-weight: bold;
                    color: #f50c0c;
                    line-height: 1;
                }
            }
        }

        .btn-container {
            margin-top: 56rpx;

            .btn {
                width: 100%;

                &.flex {
                    display: flex;
                }
            }
        }
        .bullet-comment {
            width: 100%;
            height: 350rpx;
            margin-top: 40rpx;
            .bullet-content {
                width: 100%;
                height: 350rpx;
                box-sizing: border-box;
            }
        }
        .send-message {
            margin-top: 40rpx;
        }
    }
}
</style>
