<template>
    <view class="view">
        <section class="nav-bar">
            <c-navBar title="消息" isPerch></c-navBar>
        </section>
        <section class="content">
            <view class="demo-content">
                <text class="demo-text">这是消息页面</text>
                <text class="demo-text">当前选中的 tab: {{ currentTab }}</text>
            </view>
        </section>
        <c-tabbar :selected="currentTab" @change="handleTabChange"></c-tabbar>
    </view>
</template>

<script>
export default {
    data() {
        return {
            currentTab: 3,
        };
    },
    onShow() {
        uni.hideTabBar()
    },
    methods: {
        handleTabChange(index) {
            // this.currentTab = index;
        },
    },
};
</script>

<style lang="scss" scoped></style>
