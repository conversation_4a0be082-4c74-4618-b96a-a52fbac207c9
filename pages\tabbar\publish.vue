<template>
    <view class="page">
        <section class="nav-bar">
            <c-navBar title="发布帖子" isPerch></c-navBar>
        </section>
        <section class="content">
            <view class="demo-content">
                <text class="demo-text">这是发布页面</text>
                <text class="demo-text">当前选中的 tab: {{ currentTab }}</text>
                <text class="demo-text">这个页面通过中间的突出按钮进入</text>
            </view>
        </section>
        <c-tabbar :selected="currentTab" @change="handleTabChange"></c-tabbar>
    </view>
</template>

<script>
export default {
    data() {
        return {
            currentTab: 2
        };
    },
    onShow() {
        uni.hideTabBar()
    },
    methods: {
        handleTabChange(index) {
            // this.currentTab = index;
        }
    },
};
</script>

<style lang="scss" scoped>
.page {
    min-height: 100vh;
    background: #f5f5f5;
    padding-bottom: 120rpx;
}

.content {
    padding: 24rpx;
    min-height: calc(100vh - 200rpx);
}

.demo-content {
    background: #fff;
    border-radius: 16rpx;
    padding: 40rpx;
    margin-top: 20rpx;
}

.demo-text {
    display: block;
    margin-bottom: 20rpx;
    font-size: 28rpx;
    color: #333;
    line-height: 1.6;
}
</style>
