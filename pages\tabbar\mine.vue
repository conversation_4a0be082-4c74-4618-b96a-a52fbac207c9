<template>
    <view class="view">
        <section class="nav-bar">
            <c-navBar title="我的" isPerch></c-navBar>
        </section>
        <section class="content"></section>
        <c-tabbar :selected="currentTab" @change="handleTabChange"></c-tabbar>
    </view>
</template>

<script>
export default {
    data() {
        return {
            currentTab: 4,
        };
    },
    onShow() {
        uni.hideTabBar()
    },
    methods: {
        handleTabChange(index) {
            // this.currentTab = index;
        },
    },
};
</script>

<style lang="scss" scoped></style>
